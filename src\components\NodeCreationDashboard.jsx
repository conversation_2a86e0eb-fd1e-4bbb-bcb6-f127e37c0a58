import React, { useState } from 'react';
import { Plus, Settings, Eye, Edit, Trash2, X } from 'lucide-react';
import { nodeRegistry } from '../systems/NodeSystem';
import {
  Target, Building, User, CheckSquare, Calendar, FileText
} from 'lucide-react';

const iconMap = {
  Target, Building, User, CheckSquare, Calendar, FileText
};

const NodeCreationDashboard = ({ isOpen, onClose, onCreateNode }) => {
  const [activeTab, setActiveTab] = useState('create');
  const [selectedNodeType, setSelectedNodeType] = useState(null);
  const [showNodeTypeDetails, setShowNodeTypeDetails] = useState(false);

  if (!isOpen) return null;

  const nodeTypes = nodeRegistry.getAllNodeTypes();
  const categories = [...new Set(nodeTypes.map(type => type.category))];

  const handleQuickCreate = (nodeTypeName) => {
    const randomPosition = {
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100
    };
    onCreateNode(nodeTypeName, randomPosition);
  };

  const NodeTypeCard = ({ nodeType, showActions = true }) => {
    const IconComponent = iconMap[nodeType.icon] || Target;

    return (
      <div style={{
        border: '1px solid #e5e7eb',
        borderRadius: '6px',
        padding: '10px',
        background: 'white',
        transition: 'all 0.2s ease'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '8px'
        }}>
          <div style={{
            background: nodeType.color,
            color: 'white',
            padding: '6px',
            borderRadius: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <IconComponent size={16} />
          </div>

          <div style={{ flex: 1 }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '6px'
            }}>
              <div>
                <h3 style={{
                  margin: 0,
                  fontSize: '14px',
                  fontWeight: 'bold',
                  color: '#1f2937'
                }}>
                  {nodeType.label}
                </h3>
                <div style={{
                  fontSize: '11px',
                  color: '#6b7280',
                  textTransform: 'capitalize',
                  marginTop: '1px'
                }}>
                  {nodeType.category}
                </div>
              </div>

              {showActions && (
                <div style={{ display: 'flex', gap: '3px' }}>
                  <button
                    onClick={() => {
                      setSelectedNodeType(nodeType);
                      setShowNodeTypeDetails(true);
                    }}
                    style={{
                      padding: '3px',
                      border: 'none',
                      background: '#f3f4f6',
                      borderRadius: '3px',
                      cursor: 'pointer'
                    }}
                  >
                    <Eye size={12} />
                  </button>
                  <button
                    onClick={() => handleQuickCreate(nodeType.name)}
                    style={{
                      padding: '3px',
                      border: 'none',
                      background: nodeType.color,
                      color: 'white',
                      borderRadius: '3px',
                      cursor: 'pointer'
                    }}
                  >
                    <Plus size={12} />
                  </button>
                </div>
              )}
            </div>

            <p style={{
              margin: '0 0 6px 0',
              fontSize: '12px',
              color: '#6b7280',
              lineHeight: '1.3',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }}>
              {nodeType.description}
            </p>

            <div style={{
              fontSize: '11px',
              color: '#9ca3af'
            }}>
              {nodeType.properties.length} properties
            </div>
          </div>
        </div>
      </div>
    );
  };

  const NodeTypeDetails = ({ nodeType, onClose }) => (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1100
    }}>
      <div style={{
        background: 'white',
        borderRadius: '8px',
        padding: '24px',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '80vh',
        overflow: 'auto'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <h2 style={{ margin: 0, fontSize: '20px', fontWeight: 'bold' }}>
            {nodeType.label} Details
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '4px'
            }}
          >
            <X size={20} />
          </button>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '8px' }}>
            Properties
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {nodeType.properties.map(prop => (
              <div key={prop.key} style={{
                padding: '12px',
                border: '1px solid #e5e7eb',
                borderRadius: '4px',
                background: '#f9fafb'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '4px'
                }}>
                  <span style={{ fontWeight: '500', fontSize: '14px' }}>
                    {prop.label}
                  </span>
                  <div style={{
                    display: 'flex',
                    gap: '8px',
                    alignItems: 'center'
                  }}>
                    <span style={{
                      fontSize: '12px',
                      color: '#6b7280',
                      textTransform: 'capitalize'
                    }}>
                      {prop.type}
                    </span>
                    {prop.required && (
                      <span style={{
                        fontSize: '10px',
                        background: '#ef4444',
                        color: 'white',
                        padding: '2px 4px',
                        borderRadius: '2px'
                      }}>
                        Required
                      </span>
                    )}
                  </div>
                </div>
                <div style={{ fontSize: '12px', color: '#6b7280' }}>
                  Key: {prop.key}
                </div>
                {prop.defaultValue && (
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>
                    Default: {prop.defaultValue}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={() => {
            handleQuickCreate(nodeType.name);
            onClose();
          }}
          style={{
            width: '100%',
            padding: '12px',
            background: nodeType.color,
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          Create {nodeType.label}
        </button>
      </div>
    </div>
  );

  return (
    <>
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}>
        <div style={{
          background: 'white',
          borderRadius: '6px',
          width: '85%',
          maxWidth: '700px',
          height: '75vh',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          {/* Header */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '12px 16px',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <h1 style={{ margin: 0, fontSize: '18px', fontWeight: 'bold' }}>
              Node Dashboard
            </h1>
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '2px'
              }}
            >
              <X size={18} />
            </button>
          </div>

          {/* Tabs */}
          <div style={{
            display: 'flex',
            borderBottom: '1px solid #e5e7eb',
            padding: '0 16px'
          }}>
            {[
              { id: 'create', label: 'Quick Create', icon: Plus },
              { id: 'manage', label: 'Manage Types', icon: Settings }
            ].map(tab => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                    padding: '8px 12px',
                    border: 'none',
                    background: 'none',
                    cursor: 'pointer',
                    borderBottom: activeTab === tab.id ? '2px solid #3b82f6' : '2px solid transparent',
                    color: activeTab === tab.id ? '#3b82f6' : '#6b7280',
                    fontWeight: activeTab === tab.id ? '500' : '400',
                    fontSize: '13px'
                  }}
                >
                  <IconComponent size={12} />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Content */}
          <div style={{ flex: 1, overflow: 'auto', padding: '16px' }}>
            {activeTab === 'create' && (
              <div>
                <div style={{ marginBottom: '16px' }}>
                  <h2 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '6px' }}>
                    Quick Create Nodes
                  </h2>
                  <p style={{ color: '#6b7280', margin: 0, fontSize: '13px' }}>
                    Click the + button to quickly create a new node of any type.
                  </p>
                </div>

                {categories.map(category => (
                  <div key={category} style={{ marginBottom: '18px' }}>
                    <h3 style={{
                      fontSize: '14px',
                      fontWeight: 'bold',
                      marginBottom: '8px',
                      textTransform: 'capitalize',
                      color: '#1f2937'
                    }}>
                      {category}
                    </h3>
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
                      gap: '8px'
                    }}>
                      {nodeTypes
                        .filter(type => type.category === category)
                        .map(nodeType => (
                          <NodeTypeCard key={nodeType.name} nodeType={nodeType} />
                        ))}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'manage' && (
              <div>
                <div style={{ marginBottom: '16px' }}>
                  <h2 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '6px' }}>
                    Manage Node Types
                  </h2>
                  <p style={{ color: '#6b7280', margin: 0, fontSize: '13px' }}>
                    View and manage all available node types in your system.
                  </p>
                </div>

                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
                  gap: '12px'
                }}>
                  {nodeTypes.map(nodeType => (
                    <NodeTypeCard key={nodeType.name} nodeType={nodeType} showActions={true} />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Node Type Details Modal */}
      {showNodeTypeDetails && selectedNodeType && (
        <NodeTypeDetails
          nodeType={selectedNodeType}
          onClose={() => {
            setShowNodeTypeDetails(false);
            setSelectedNodeType(null);
          }}
        />
      )}
    </>
  );
};

export default NodeCreationDashboard;
