import React, { useState, useEffect } from 'react';
import { X, Edit, DollarSign, Calendar, TrendingUp, Building, User, Clock, Trash2 } from 'lucide-react';
import { opportunityService, activityService } from '../services/database';

const OpportunityDetails = ({ opportunity, onClose, onEdit }) => {
  const [activities, setActivities] = useState([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (opportunity?.id) {
      try {
        const activitiesData = activityService.getByOpportunity(opportunity.id);
        setActivities(activitiesData);
      } catch (error) {
        console.error('Error loading activities:', error);
      }
    }
  }, [opportunity]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No date set';
    return new Date(dateString).toLocaleDateString();
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'No date';
    return new Date(dateString).toLocaleString();
  };

  const getStageColor = (stage) => {
    const colors = {
      lead: '#6b7280',
      qualified: '#3b82f6',
      proposal: '#f59e0b',
      negotiation: '#10b981',
      closed_won: '#22c55e',
      closed_lost: '#ef4444'
    };
    return colors[stage] || '#6b7280';
  };

  const handleDelete = () => {
    try {
      opportunityService.delete(opportunity.id);
      onClose();
    } catch (error) {
      console.error('Error deleting opportunity:', error);
      alert('Error deleting opportunity. Please try again.');
    }
  };

  const addActivity = (type, description) => {
    try {
      activityService.create({
        opportunity_id: opportunity.id,
        type,
        description
      });
      
      // Reload activities
      const activitiesData = activityService.getByOpportunity(opportunity.id);
      setActivities(activitiesData);
    } catch (error) {
      console.error('Error adding activity:', error);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '8px',
        width: '90%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div>
            <h2 style={{ margin: '0 0 8px 0', fontSize: '24px', fontWeight: 'bold' }}>
              {opportunity.title}
            </h2>
            <div 
              style={{
                background: getStageColor(opportunity.stage),
                color: 'white',
                padding: '4px 8px',
                borderRadius: '4px',
                fontSize: '12px',
                fontWeight: 'bold',
                textTransform: 'uppercase',
                display: 'inline-block'
              }}
            >
              {opportunity.stage.replace('_', ' ')}
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={() => onEdit(opportunity)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              <Edit size={16} />
              Edit
            </button>
            
            <button
              onClick={() => setShowDeleteConfirm(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              <Trash2 size={16} />
              Delete
            </button>
            
            <button
              onClick={onClose}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                padding: '4px'
              }}
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div style={{ flex: 1, overflow: 'auto', padding: '24px' }}>
          {/* Key Metrics */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px',
            marginBottom: '24px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              padding: '16px',
              background: '#f0fdf4',
              borderRadius: '8px'
            }}>
              <DollarSign size={24} style={{ color: '#059669' }} />
              <div>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937' }}>
                  {formatCurrency(opportunity.value || 0)}
                </div>
                <div style={{ fontSize: '14px', color: '#6b7280' }}>
                  Opportunity Value
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              padding: '16px',
              background: '#fef3c7',
              borderRadius: '8px'
            }}>
              <TrendingUp size={24} style={{ color: '#d97706' }} />
              <div>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1f2937' }}>
                  {opportunity.probability || 0}%
                </div>
                <div style={{ fontSize: '14px', color: '#6b7280' }}>
                  Win Probability
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              padding: '16px',
              background: '#fdf2f8',
              borderRadius: '8px'
            }}>
              <Calendar size={24} style={{ color: '#be185d' }} />
              <div>
                <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1f2937' }}>
                  {formatDate(opportunity.expected_close_date)}
                </div>
                <div style={{ fontSize: '14px', color: '#6b7280' }}>
                  Expected Close
                </div>
              </div>
            </div>
          </div>

          {/* Details */}
          <div style={{ marginBottom: '24px' }}>
            <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
              Details
            </h3>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '16px' }}>
              {opportunity.company_name && (
                <div>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                    <Building size={16} style={{ marginRight: '6px', color: '#6b7280' }} />
                    <span style={{ fontSize: '14px', fontWeight: '500', color: '#374151' }}>Company</span>
                  </div>
                  <div style={{ fontSize: '16px', color: '#1f2937' }}>
                    {opportunity.company_name}
                  </div>
                </div>
              )}

              {opportunity.contact_name && (
                <div>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                    <User size={16} style={{ marginRight: '6px', color: '#6b7280' }} />
                    <span style={{ fontSize: '14px', fontWeight: '500', color: '#374151' }}>Contact</span>
                  </div>
                  <div style={{ fontSize: '16px', color: '#1f2937' }}>
                    {opportunity.contact_name}
                  </div>
                </div>
              )}
            </div>

            {opportunity.description && (
              <div>
                <div style={{ fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '8px' }}>
                  Description
                </div>
                <div style={{ 
                  fontSize: '14px', 
                  color: '#6b7280', 
                  lineHeight: '1.5',
                  padding: '12px',
                  background: '#f9fafb',
                  borderRadius: '4px'
                }}>
                  {opportunity.description}
                </div>
              </div>
            )}
          </div>

          {/* Activities */}
          <div>
            <h3 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '16px' }}>
              Recent Activities
            </h3>
            
            {activities.length > 0 ? (
              <div style={{ space: '12px' }}>
                {activities.map((activity, index) => (
                  <div key={activity.id} style={{
                    display: 'flex',
                    gap: '12px',
                    padding: '12px',
                    background: index % 2 === 0 ? '#f9fafb' : 'white',
                    borderRadius: '4px',
                    marginBottom: '8px'
                  }}>
                    <Clock size={16} style={{ color: '#6b7280', marginTop: '2px' }} />
                    <div style={{ flex: 1 }}>
                      <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
                        {activity.type}
                      </div>
                      <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '4px' }}>
                        {activity.description}
                      </div>
                      <div style={{ fontSize: '12px', color: '#9ca3af' }}>
                        {formatDateTime(activity.date)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                color: '#6b7280', 
                fontSize: '14px',
                padding: '24px'
              }}>
                No activities recorded yet
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Delete Confirmation */}
      {showDeleteConfirm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1100
        }}>
          <div style={{
            background: 'white',
            borderRadius: '8px',
            padding: '24px',
            maxWidth: '400px'
          }}>
            <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: 'bold' }}>
              Delete Opportunity
            </h3>
            <p style={{ margin: '0 0 24px 0', color: '#6b7280' }}>
              Are you sure you want to delete "{opportunity.title}"? This action cannot be undone.
            </p>
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                style={{
                  padding: '8px 16px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px',
                  background: 'white',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                style={{
                  padding: '8px 16px',
                  background: '#ef4444',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OpportunityDetails;
