{"name": "crm-opportunities", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-oauth/google": "^0.12.2", "@xyflow/react": "^12.7.0", "better-sqlite3": "^11.10.0", "date-fns": "^4.1.0", "firebase": "^11.9.1", "google-auth-library": "^10.1.0", "lucide-react": "^0.522.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}