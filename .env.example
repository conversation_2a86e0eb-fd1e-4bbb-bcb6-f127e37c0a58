# Google OAuth Configuration
# Copy this file to .env and replace with your actual Google Client ID

# Get your Google Client ID from: https://console.cloud.google.com/
# Follow the setup guide in GOOGLE_OAUTH_SETUP.md
VITE_GOOGLE_CLIENT_ID=your-google-client-id-here.apps.googleusercontent.com

# Firebase Configuration
# Get these values from Firebase Console > Project Settings > Your apps
# Follow the setup guide in FIREBASE_SETUP.md
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789012
VITE_FIREBASE_APP_ID=your-firebase-app-id

# Demo Mode
# Set to 'true' to always use demo mode, 'false' to try Google OAuth first
VITE_DEMO_MODE=false
