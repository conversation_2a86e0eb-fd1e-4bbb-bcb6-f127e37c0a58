// Firebase database service
import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  onSnapshot,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from './config';

// Collection names
const COLLECTIONS = {
  NODES: 'nodes',
  EDGES: 'edges',
  OPPORTUNITIES: 'opportunities',
  COMPANIES: 'companies',
  CONTACTS: 'contacts',
  ACTIVITIES: 'activities'
};

// Generic CRUD operations
export class FirebaseService {
  constructor(collectionName) {
    this.collectionName = collectionName;
    this.collectionRef = collection(db, collectionName);
  }

  // Create a new document
  async create(data) {
    try {
      const docData = {
        ...data,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp()
      };
      const docRef = await addDoc(this.collectionRef, docData);
      return docRef.id;
    } catch (error) {
      console.error(`Error creating document in ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Get all documents
  async getAll() {
    try {
      const querySnapshot = await getDocs(this.collectionRef);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error(`Error getting documents from ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Get document by ID
  async getById(id) {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        };
      } else {
        return null;
      }
    } catch (error) {
      console.error(`Error getting document ${id} from ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Update document
  async update(id, data) {
    try {
      const docRef = doc(db, this.collectionName, id);
      const updateData = {
        ...data,
        updated_at: serverTimestamp()
      };
      await updateDoc(docRef, updateData);
      return true;
    } catch (error) {
      console.error(`Error updating document ${id} in ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Delete document
  async delete(id) {
    try {
      const docRef = doc(db, this.collectionName, id);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error(`Error deleting document ${id} from ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Query documents with conditions
  async query(conditions = [], orderByField = null, orderDirection = 'asc') {
    try {
      let q = this.collectionRef;
      
      // Add where conditions
      conditions.forEach(condition => {
        q = query(q, where(condition.field, condition.operator, condition.value));
      });
      
      // Add ordering
      if (orderByField) {
        q = query(q, orderBy(orderByField, orderDirection));
      }
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error(`Error querying ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Real-time listener
  onSnapshot(callback, conditions = [], orderByField = null) {
    try {
      let q = this.collectionRef;
      
      // Add where conditions
      conditions.forEach(condition => {
        q = query(q, where(condition.field, condition.operator, condition.value));
      });
      
      // Add ordering
      if (orderByField) {
        q = query(q, orderBy(orderByField));
      }
      
      return onSnapshot(q, (querySnapshot) => {
        const docs = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        callback(docs);
      });
    } catch (error) {
      console.error(`Error setting up listener for ${this.collectionName}:`, error);
      throw error;
    }
  }
}

// Specific service instances
export const nodesService = new FirebaseService(COLLECTIONS.NODES);
export const edgesService = new FirebaseService(COLLECTIONS.EDGES);
export const opportunitiesService = new FirebaseService(COLLECTIONS.OPPORTUNITIES);
export const companiesService = new FirebaseService(COLLECTIONS.COMPANIES);
export const contactsService = new FirebaseService(COLLECTIONS.CONTACTS);
export const activitiesService = new FirebaseService(COLLECTIONS.ACTIVITIES);

// User-specific data operations
export class UserDataService {
  constructor(userId) {
    this.userId = userId;
  }

  // Get user's nodes
  async getUserNodes() {
    try {
      return await nodesService.query([
        { field: 'userId', operator: '==', value: this.userId }
      ], 'created_at');
    } catch (error) {
      console.error('Error getting user nodes:', error);
      return [];
    }
  }

  // Get user's edges
  async getUserEdges() {
    try {
      return await edgesService.query([
        { field: 'userId', operator: '==', value: this.userId }
      ]);
    } catch (error) {
      console.error('Error getting user edges:', error);
      return [];
    }
  }

  // Update node position
  async updateNodePosition(nodeId, x, y) {
    try {
      await nodesService.update(nodeId, {
        'position.x': x,
        'position.y': y
      });
      return true;
    } catch (error) {
      console.error('Error updating node position:', error);
      return false;
    }
  }

  // Create user node
  async createNode(nodeData) {
    try {
      const data = {
        ...nodeData,
        userId: this.userId
      };
      return await nodesService.create(data);
    } catch (error) {
      console.error('Error creating node:', error);
      throw error;
    }
  }

  // Create user edge
  async createEdge(edgeData) {
    try {
      const data = {
        ...edgeData,
        userId: this.userId
      };
      return await edgesService.create(data);
    } catch (error) {
      console.error('Error creating edge:', error);
      throw error;
    }
  }

  // Real-time listeners for user data
  onNodesSnapshot(callback) {
    return nodesService.onSnapshot(callback, [
      { field: 'userId', operator: '==', value: this.userId }
    ], 'created_at');
  }

  onEdgesSnapshot(callback) {
    return edgesService.onSnapshot(callback, [
      { field: 'userId', operator: '==', value: this.userId }
    ]);
  }
}

export default FirebaseService;
