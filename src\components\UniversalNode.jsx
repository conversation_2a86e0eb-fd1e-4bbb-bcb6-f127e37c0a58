import React from 'react';
import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { 
  Target, Building, User, CheckSquare, Calendar, FileText, 
  DollarSign, TrendingUp, Clock, MapPin, Phone, Mail, Globe
} from 'lucide-react';

// Icon mapping for different node types
const iconMap = {
  Target, Building, User, CheckSquare, Calendar, FileText,
  DollarSign, TrendingUp, Clock, MapPin, Phone, Mail, Globe
};

const UniversalNode = ({ data, selected }) => {
  const nodeType = data.nodeType || 'opportunity';
  const properties = data.properties || [];
  
  // Get node configuration
  const getNodeConfig = () => {
    const configs = {
      opportunity: { icon: 'Target', color: '#3b82f6' },
      company: { icon: 'Building', color: '#059669' },
      contact: { icon: 'User', color: '#7c3aed' },
      task: { icon: 'CheckSquare', color: '#f59e0b' },
      meeting: { icon: 'Calendar', color: '#ef4444' },
      note: { icon: 'FileText', color: '#6b7280' }
    };
    return configs[nodeType] || configs.opportunity;
  };

  const config = getNodeConfig();
  const IconComponent = iconMap[config.icon] || Target;

  // Get primary display properties based on node type
  const getPrimaryProperties = () => {
    switch (nodeType) {
      case 'opportunity':
        return {
          title: data.title || 'Untitled Opportunity',
          subtitle: data.company_name || data.stage || 'Lead',
          value: data.value,
          probability: data.probability,
          date: data.expected_close_date
        };
      case 'company':
        return {
          title: data.name || 'Untitled Company',
          subtitle: data.industry || 'Company',
          value: null,
          info: data.website || data.email
        };
      case 'contact':
        return {
          title: `${data.first_name || ''} ${data.last_name || ''}`.trim() || 'Untitled Contact',
          subtitle: data.position || data.company_name || 'Contact',
          info: data.email || data.phone
        };
      case 'task':
        return {
          title: data.title || 'Untitled Task',
          subtitle: data.status || 'Pending',
          priority: data.priority,
          date: data.due_date
        };
      case 'meeting':
        return {
          title: data.title || 'Untitled Meeting',
          subtitle: data.meeting_type || 'Meeting',
          date: data.date,
          duration: data.duration
        };
      case 'note':
        return {
          title: data.title || 'Untitled Note',
          subtitle: 'Note',
          content: data.content
        };
      default:
        return {
          title: data.title || 'Untitled Node',
          subtitle: nodeType
        };
    }
  };

  const primaryProps = getPrimaryProperties();

  const formatCurrency = (amount) => {
    if (!amount) return null;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString();
  };

  const getPriorityColor = (priority) => {
    const colors = {
      low: '#6b7280',
      medium: '#f59e0b',
      high: '#ef4444',
      urgent: '#dc2626'
    };
    return colors[priority] || '#6b7280';
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: '#6b7280',
      in_progress: '#3b82f6',
      completed: '#22c55e',
      cancelled: '#ef4444',
      lead: '#6b7280',
      qualified: '#3b82f6',
      proposal: '#f59e0b',
      negotiation: '#10b981',
      closed_won: '#22c55e',
      closed_lost: '#ef4444'
    };
    return colors[status] || '#6b7280';
  };

  return (
    <div 
      className={`universal-node ${selected ? 'selected' : ''}`}
      style={{
        background: 'white',
        border: `2px solid ${config.color}`,
        borderRadius: '8px',
        padding: '12px',
        minWidth: '280px',
        maxWidth: '320px',
        boxShadow: selected ? '0 4px 12px rgba(0,0,0,0.15)' : '0 2px 8px rgba(0,0,0,0.1)',
        transition: 'box-shadow 0.2s ease'
      }}
    >
      {/* Header with icon and type */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        marginBottom: '8px'
      }}>
        <div style={{
          background: config.color,
          color: 'white',
          padding: '6px',
          borderRadius: '4px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <IconComponent size={16} />
        </div>
        <div style={{
          background: config.color,
          color: 'white',
          padding: '2px 8px',
          borderRadius: '12px',
          fontSize: '10px',
          fontWeight: 'bold',
          textTransform: 'uppercase'
        }}>
          {nodeType}
        </div>
      </div>

      {/* Title */}
      <h3 style={{ 
        margin: '0 0 8px 0', 
        fontSize: '16px', 
        fontWeight: 'bold',
        color: '#1f2937',
        lineHeight: '1.2'
      }}>
        {primaryProps.title}
      </h3>

      {/* Subtitle */}
      <div style={{
        fontSize: '14px',
        color: '#6b7280',
        marginBottom: '8px',
        display: 'flex',
        alignItems: 'center',
        gap: '4px'
      }}>
        {primaryProps.subtitle && (
          <span style={{
            background: getStatusColor(primaryProps.subtitle.toLowerCase()),
            color: 'white',
            padding: '2px 6px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: '500'
          }}>
            {primaryProps.subtitle}
          </span>
        )}
      </div>

      {/* Dynamic content based on node type */}
      <div style={{ marginBottom: '8px' }}>
        {/* Value and Probability for opportunities */}
        {nodeType === 'opportunity' && (
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
            {primaryProps.value && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <DollarSign size={14} style={{ color: '#059669' }} />
                <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#059669' }}>
                  {formatCurrency(primaryProps.value)}
                </span>
              </div>
            )}
            {primaryProps.probability !== null && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <TrendingUp size={14} style={{ color: '#3b82f6' }} />
                <span style={{ fontSize: '14px', color: '#3b82f6' }}>
                  {primaryProps.probability}%
                </span>
              </div>
            )}
          </div>
        )}

        {/* Priority for tasks */}
        {nodeType === 'task' && primaryProps.priority && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginBottom: '4px' }}>
            <div style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: getPriorityColor(primaryProps.priority)
            }} />
            <span style={{ fontSize: '12px', color: '#6b7280', textTransform: 'capitalize' }}>
              {primaryProps.priority} Priority
            </span>
          </div>
        )}

        {/* Duration for meetings */}
        {nodeType === 'meeting' && primaryProps.duration && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px', marginBottom: '4px' }}>
            <Clock size={14} style={{ color: '#6b7280' }} />
            <span style={{ fontSize: '12px', color: '#6b7280' }}>
              {primaryProps.duration} minutes
            </span>
          </div>
        )}

        {/* Info line */}
        {primaryProps.info && (
          <div style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px' }}>
            {primaryProps.info}
          </div>
        )}

        {/* Date */}
        {primaryProps.date && (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Calendar size={14} style={{ color: '#6b7280' }} />
            <span style={{ fontSize: '12px', color: '#6b7280' }}>
              {formatDate(primaryProps.date)}
            </span>
          </div>
        )}

        {/* Content preview for notes */}
        {nodeType === 'note' && primaryProps.content && (
          <div style={{ 
            fontSize: '12px', 
            color: '#6b7280', 
            marginTop: '8px',
            borderTop: '1px solid #e5e7eb',
            paddingTop: '8px',
            fontStyle: 'italic'
          }}>
            {primaryProps.content.length > 80 
              ? `${primaryProps.content.substring(0, 80)}...` 
              : primaryProps.content
            }
          </div>
        )}
      </div>

      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: config.color, border: '2px solid white' }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: config.color, border: '2px solid white' }}
      />
    </div>
  );
};

export default UniversalNode;
