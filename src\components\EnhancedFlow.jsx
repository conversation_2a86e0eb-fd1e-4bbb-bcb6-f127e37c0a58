import React, { useState, useCallback, useEffect } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
  useReactFlow
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import UniversalNode from './UniversalNode';
import NodeTypeSelector from './NodeTypeSelector';
import NodeCreationDashboard from './NodeCreationDashboard';
import { nodeRegistry } from '../systems/NodeSystem';
import { UserDataService } from '../firebase/database';
import { opportunityService } from '../services/database';
import { Plus, RefreshCw, Grid, Settings, Cloud, CloudOff } from 'lucide-react';

const nodeTypes = {
  opportunity: UniversalNode,
  company: UniversalNode,
  contact: UniversalNode,
  task: UniversalNode,
  meeting: UniversalNode,
  note: UniversalNode,
};

const EnhancedFlow = ({ onNodeSelect, user }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showNodeSelector, setShowNodeSelector] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [selectorPosition, setSelectorPosition] = useState({ x: 0, y: 0 });
  const [pendingConnection, setPendingConnection] = useState(null);
  const [isOnline, setIsOnline] = useState(true);
  const [userDataService, setUserDataService] = useState(null);

  const { screenToFlowPosition } = useReactFlow();

  // Initialize Firebase service when user changes
  useEffect(() => {
    if (user?.id) {
      const service = new UserDataService(user.id);
      setUserDataService(service);
    } else {
      setUserDataService(null);
    }
  }, [user]);

  // Load all nodes from Firebase and fallback to localStorage
  const loadNodes = useCallback(async () => {
    setIsLoading(true);
    try {
      if (userDataService && isOnline) {
        // Load from Firebase
        const [firebaseNodes, firebaseEdges] = await Promise.all([
          userDataService.getUserNodes(),
          userDataService.getUserEdges()
        ]);

        // Convert Firebase nodes to flow format
        const flowNodes = firebaseNodes.map(node => ({
          id: node.id,
          type: node.type || 'opportunity',
          position: node.position || { x: 0, y: 0 },
          data: {
            ...node.data,
            nodeType: node.type
          }
        }));

        setNodes(flowNodes);
        setEdges(firebaseEdges);

        console.log(`Loaded ${flowNodes.length} nodes and ${firebaseEdges.length} edges from Firebase`);
      } else {
        // Fallback to localStorage/legacy data
        await loadLegacyData();
      }

    } catch (error) {
      console.error('Error loading from Firebase, falling back to localStorage:', error);
      setIsOnline(false);
      await loadLegacyData();
    } finally {
      setIsLoading(false);
    }
  }, [userDataService, isOnline, setNodes, setEdges]);

  // Load legacy data from localStorage
  const loadLegacyData = useCallback(async () => {
    try {
      // Load opportunities from existing database
      const opportunities = opportunityService.getAll();

      const flowNodes = opportunities.map((opp, index) => {
        const node = nodeRegistry.createNode('opportunity', {
          ...opp,
          position: {
            x: opp.position_x || (index % 3) * 320 + 50,
            y: opp.position_y || Math.floor(index / 3) * 200 + 50
          }
        });
        return node.toFlowNode();
      });

      // Load other node types from localStorage if they exist
      const savedNodes = localStorage.getItem('crm_enhanced_nodes');
      if (savedNodes) {
        const parsedNodes = JSON.parse(savedNodes);
        flowNodes.push(...parsedNodes.filter(node => node.type !== 'opportunity'));
      }

      setNodes(flowNodes);

      // Load edges
      const savedEdges = localStorage.getItem('crm_enhanced_edges');
      if (savedEdges) {
        setEdges(JSON.parse(savedEdges));
      }

    } catch (error) {
      console.error('Error loading legacy data:', error);
    }
  }, [setNodes, setEdges]);

  // Save nodes and edges to localStorage
  const saveNodesAndEdges = useCallback((currentNodes, currentEdges) => {
    const nonOpportunityNodes = currentNodes.filter(node => node.type !== 'opportunity');
    localStorage.setItem('crm_enhanced_nodes', JSON.stringify(nonOpportunityNodes));
    localStorage.setItem('crm_enhanced_edges', JSON.stringify(currentEdges));
  }, []);

  // Load data on component mount
  useEffect(() => {
    loadNodes();
  }, [loadNodes]);

  // Handle node drag end - save position to Firebase
  const onNodeDragStop = useCallback(async (event, node) => {
    try {
      if (userDataService && isOnline) {
        // Save to Firebase
        await userDataService.updateNodePosition(node.id, node.position.x, node.position.y);
        console.log(`Updated node ${node.id} position in Firebase:`, node.position);
      } else {
        // Fallback to localStorage for legacy nodes
        if (node.type === 'opportunity') {
          opportunityService.updatePosition(
            parseInt(node.id),
            node.position.x,
            node.position.y
          );
        } else {
          setNodes(currentNodes => {
            const updatedNodes = currentNodes.map(n =>
              n.id === node.id ? { ...n, position: node.position } : n
            );
            saveNodesAndEdges(updatedNodes, edges);
            return updatedNodes;
          });
        }
      }
    } catch (error) {
      console.error('Error updating node position:', error);
      // Fallback to localStorage on Firebase error
      setNodes(currentNodes => {
        const updatedNodes = currentNodes.map(n =>
          n.id === node.id ? { ...n, position: node.position } : n
        );
        saveNodesAndEdges(updatedNodes, edges);
        return updatedNodes;
      });
    }
  }, [userDataService, isOnline, setNodes, edges, saveNodesAndEdges]);

  // Handle node click
  const onNodeClick = useCallback((event, node) => {
    if (onNodeSelect) {
      onNodeSelect(node.data);
    }
  }, [onNodeSelect]);

  // Handle edge connection
  const onConnect = useCallback((params) => {
    setEdges((eds) => {
      const newEdges = addEdge(params, eds);
      saveNodesAndEdges(nodes, newEdges);
      return newEdges;
    });
  }, [setEdges, nodes, saveNodesAndEdges]);

  // Handle connection start (for edge-drop node creation)
  const onConnectStart = useCallback((event, { nodeId, handleType }) => {
    setPendingConnection({ nodeId, handleType });
  }, []);

  // Handle connection end (for edge-drop node creation)
  const onConnectEnd = useCallback((event) => {
    if (pendingConnection) {
      const targetIsPane = event.target.classList.contains('react-flow__pane');

      if (targetIsPane) {
        // Get the position where the connection was dropped
        const position = screenToFlowPosition({
          x: event.clientX,
          y: event.clientY,
        });

        setSelectorPosition(position);
        setShowNodeSelector(true);
      }

      setPendingConnection(null);
    }
  }, [pendingConnection, screenToFlowPosition]);

  // Create new node
  const createNode = useCallback(async (nodeTypeName, position) => {
    try {
      const newNode = nodeRegistry.createNode(nodeTypeName, { position });
      const flowNode = newNode.toFlowNode();

      if (userDataService && isOnline) {
        // Save to Firebase
        const nodeData = {
          type: nodeTypeName,
          position: position,
          data: flowNode.data
        };

        const firebaseNodeId = await userDataService.createNode(nodeData);
        flowNode.id = firebaseNodeId; // Use Firebase-generated ID

        console.log(`Created node ${firebaseNodeId} in Firebase`);
      }

      setNodes((nds) => {
        const updatedNodes = nds.concat(flowNode);
        if (!userDataService || !isOnline) {
          saveNodesAndEdges(updatedNodes, edges);
        }
        return updatedNodes;
      });

      // If there was a pending connection, create the edge
      if (pendingConnection) {
        const newEdge = {
          id: `${pendingConnection.nodeId}-${flowNode.id}`,
          source: pendingConnection.handleType === 'source' ? pendingConnection.nodeId : flowNode.id,
          target: pendingConnection.handleType === 'source' ? flowNode.id : pendingConnection.nodeId,
        };

        if (userDataService && isOnline) {
          // Save edge to Firebase
          await userDataService.createEdge(newEdge);
          console.log(`Created edge in Firebase:`, newEdge);
        }

        setEdges((eds) => {
          const updatedEdges = eds.concat(newEdge);
          if (!userDataService || !isOnline) {
            saveNodesAndEdges(nodes.concat(flowNode), updatedEdges);
          }
          return updatedEdges;
        });
      }

      setShowNodeSelector(false);
      setPendingConnection(null);
    } catch (error) {
      console.error('Error creating node:', error);
      // Fallback to localStorage
      const newNode = nodeRegistry.createNode(nodeTypeName, { position });
      const flowNode = newNode.toFlowNode();

      setNodes((nds) => {
        const updatedNodes = nds.concat(flowNode);
        saveNodesAndEdges(updatedNodes, edges);
        return updatedNodes;
      });

      setShowNodeSelector(false);
      setPendingConnection(null);
    }
  }, [userDataService, isOnline, setNodes, setEdges, edges, nodes, pendingConnection, saveNodesAndEdges]);

  // Handle pane click to create nodes
  const onPaneClick = useCallback((event) => {
    // Only show selector if not dragging and no pending connection
    if (!pendingConnection && event.detail === 2) { // Double click
      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });
      setSelectorPosition(position);
      setShowNodeSelector(true);
    }
  }, [screenToFlowPosition, pendingConnection]);

  if (isLoading) {
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '18px',
        color: '#6b7280'
      }}>
        Loading enhanced flow...
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onConnectStart={onConnectStart}
        onConnectEnd={onConnectEnd}
        onNodeClick={onNodeClick}
        onNodeDragStop={onNodeDragStop}
        onPaneClick={onPaneClick}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
        connectionLineStyle={{ stroke: '#3b82f6', strokeWidth: 2 }}
        defaultEdgeOptions={{ style: { stroke: '#3b82f6', strokeWidth: 2 } }}
      >
        <Background color="#f1f5f9" gap={20} />
        <Controls />
        <MiniMap
          nodeColor={(node) => {
            const configs = {
              opportunity: '#3b82f6',
              company: '#059669',
              contact: '#7c3aed',
              task: '#f59e0b',
              meeting: '#ef4444',
              note: '#6b7280'
            };
            return configs[node.type] || '#6b7280';
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
        />

        {/* Enhanced Control Panel */}
        <Panel position="top-right">
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {/* Firebase Status Indicator */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 10px',
              background: isOnline && userDataService ? '#dcfce7' : '#fef3c7',
              color: isOnline && userDataService ? '#166534' : '#92400e',
              borderRadius: '6px',
              fontSize: '12px',
              fontWeight: '500'
            }}>
              {isOnline && userDataService ? <Cloud size={14} /> : <CloudOff size={14} />}
              {isOnline && userDataService ? 'Firebase Connected' : 'Offline Mode'}
            </div>

            <button
              onClick={() => setShowDashboard(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Grid size={16} />
              Dashboard
            </button>

            <button
              onClick={() => {
                setSelectorPosition({ x: 200, y: 200 });
                setShowNodeSelector(true);
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#059669',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Plus size={16} />
              Add Node
            </button>

            <button
              onClick={loadNodes}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <RefreshCw size={16} />
              Refresh
            </button>
          </div>
        </Panel>

        {/* Instructions Panel */}
        <Panel position="bottom-left">
          <div style={{
            background: 'white',
            padding: '12px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            fontSize: '12px',
            maxWidth: '300px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>Quick Actions</div>
            <div style={{ marginBottom: '4px' }}>• Double-click empty space to create node</div>
            <div style={{ marginBottom: '4px' }}>• Drag from node handle and drop to create connected node</div>
            <div style={{ marginBottom: '4px' }}>• Click Dashboard for advanced node management</div>
            <div>• Drag nodes to reposition them</div>
          </div>
        </Panel>
      </ReactFlow>

      {/* Node Type Selector Modal */}
      <NodeTypeSelector
        isOpen={showNodeSelector}
        onClose={() => {
          setShowNodeSelector(false);
          setPendingConnection(null);
        }}
        onSelectType={createNode}
        position={selectorPosition}
      />

      {/* Node Creation Dashboard */}
      <NodeCreationDashboard
        isOpen={showDashboard}
        onClose={() => setShowDashboard(false)}
        onCreateNode={createNode}
      />
    </div>
  );
};

export default EnhancedFlow;
