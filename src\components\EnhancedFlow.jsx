import React, { useState, useCallback, useEffect } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
  useReactFlow
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import UniversalNode from './UniversalNode';
import NodeTypeSelector from './NodeTypeSelector';
import NodeCreationDashboard from './NodeCreationDashboard';
import { nodeRegistry } from '../systems/NodeSystem';
import { UserDataService } from '../firebase/database';
import { opportunityService } from '../services/database';
import { auth } from '../firebase/config';
import { Plus, RefreshCw, Grid, Settings, Cloud, CloudOff } from 'lucide-react';

const nodeTypes = {
  opportunity: UniversalNode,
  company: UniversalNode,
  contact: UniversalNode,
  task: UniversalNode,
  meeting: UniversalNode,
  note: UniversalNode,
};

const EnhancedFlow = ({ onNodeSelect, onFirebaseServiceReady, user }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showNodeSelector, setShowNodeSelector] = useState(false);
  const [showDashboard, setShowDashboard] = useState(false);
  const [selectorPosition, setSelectorPosition] = useState({ x: 0, y: 0 });
  const [pendingConnection, setPendingConnection] = useState(null);
  const [isOnline, setIsOnline] = useState(true);
  const [userDataService, setUserDataService] = useState(null);
  const [snapToGrid, setSnapToGrid] = useState(true);

  // Grid settings
  const gridSize = 20;

  const { screenToFlowPosition } = useReactFlow();

  // Helper function to snap position to grid
  const snapToGridPosition = useCallback((position) => {
    if (!snapToGrid) return position;
    return {
      x: Math.round(position.x / gridSize) * gridSize,
      y: Math.round(position.y / gridSize) * gridSize,
    };
  }, [snapToGrid, gridSize]);

  // Initialize Firebase service when user changes
  useEffect(() => {
    if (user?.id) {
      const service = new UserDataService(user.id);
      setUserDataService(service);
      // Notify parent component that Firebase service is ready
      if (onFirebaseServiceReady) {
        onFirebaseServiceReady(service);
      }
    } else {
      setUserDataService(null);
      if (onFirebaseServiceReady) {
        onFirebaseServiceReady(null);
      }
    }
  }, [user, onFirebaseServiceReady]);

  // Load all nodes from Firebase and fallback to localStorage
  const loadNodes = useCallback(async () => {
    console.log('🔍 loadNodes called - current loading state:', isLoading);

    // Prevent multiple simultaneous loads
    if (isLoading) {
      console.log('🔍 Already loading, skipping...');
      return;
    }

    console.log('🔍 Setting loading to true');
    setIsLoading(true);
    try {
      // Debug: Check Firebase auth state
      console.log('🔍 Debug - Current Firebase user:', auth.currentUser);
      console.log('🔍 Debug - User object:', user);
      console.log('🔍 Debug - UserDataService exists:', !!userDataService);

      if (userDataService && isOnline && user?.id && auth.currentUser) {
        // Load from Firebase only if user is authenticated with Firebase (not demo mode)
        console.log('🔍 Debug - Attempting Firebase query with user ID:', user.id);

        // Add timeout to prevent infinite loading
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Firebase query timeout')), 10000)
        );

        const [firebaseNodes, firebaseEdges] = await Promise.race([
          Promise.all([
            userDataService.getUserNodes(),
            userDataService.getUserEdges()
          ]),
          timeoutPromise
        ]);

        // Convert Firebase nodes to flow format
        const flowNodes = firebaseNodes.map(node => ({
          id: node.id,
          type: node.type || 'opportunity',
          position: node.position || { x: 0, y: 0 },
          data: {
            ...node.data,
            nodeType: node.type
          }
        }));

        console.log('🔍 Setting nodes and edges in state');
        setNodes(flowNodes);
        setEdges(firebaseEdges);

        console.log(`Loaded ${flowNodes.length} nodes and ${firebaseEdges.length} edges from Firebase`);
      } else {
        // Fallback to localStorage/legacy data when not authenticated or offline
        console.log('🔍 Loading legacy data');
        await loadLegacyData();
      }

    } catch (error) {
      console.error('Error loading from Firebase, falling back to localStorage:', error);
      console.error('Firebase error details:', error.message);
      setIsOnline(false);
      await loadLegacyData();
    } finally {
      console.log('🔍 loadNodes finished - setting loading to false');
      setIsLoading(false);
    }
  }, [userDataService, isOnline, user, isLoading]);

  // Load legacy data from localStorage
  const loadLegacyData = useCallback(async () => {
    try {
      // Load opportunities from existing database
      const opportunities = opportunityService.getAll();

      const flowNodes = opportunities.map((opp, index) => {
        const node = nodeRegistry.createNode('opportunity', {
          ...opp,
          position: {
            x: opp.position_x || (index % 3) * 320 + 50,
            y: opp.position_y || Math.floor(index / 3) * 200 + 50
          }
        });
        return node.toFlowNode();
      });

      // Load other node types from localStorage if they exist
      const savedNodes = localStorage.getItem('crm_enhanced_nodes');
      if (savedNodes) {
        const parsedNodes = JSON.parse(savedNodes);
        flowNodes.push(...parsedNodes.filter(node => node.type !== 'opportunity'));
      }

      setNodes(flowNodes);

      // Load edges
      const savedEdges = localStorage.getItem('crm_enhanced_edges');
      if (savedEdges) {
        setEdges(JSON.parse(savedEdges));
      }

    } catch (error) {
      console.error('Error loading legacy data:', error);
    }
  }, [setNodes, setEdges]);

  // Save nodes and edges to localStorage
  const saveNodesAndEdges = useCallback((currentNodes, currentEdges) => {
    const nonOpportunityNodes = currentNodes.filter(node => node.type !== 'opportunity');
    localStorage.setItem('crm_enhanced_nodes', JSON.stringify(nonOpportunityNodes));
    localStorage.setItem('crm_enhanced_edges', JSON.stringify(currentEdges));
  }, []);

  // Load data when user service is ready or when user changes
  useEffect(() => {
    console.log('🔍 useEffect triggered - user:', user?.id, 'userDataService:', !!userDataService);
    // Only load nodes if we have a user (authenticated) or if we're in offline mode
    if (user?.id || !user) {
      loadNodes();
    }
  }, [loadNodes, user]);

  // Handle node drag end - save position to Firebase
  const onNodeDragStop = useCallback(async (event, node) => {
    try {
      // Snap node position to grid
      const snappedPosition = snapToGridPosition(node.position);

      // Update node position in state if it was snapped
      if (snappedPosition.x !== node.position.x || snappedPosition.y !== node.position.y) {
        setNodes((nds) =>
          nds.map((n) =>
            n.id === node.id ? { ...n, position: snappedPosition } : n
          )
        );
      }

      if (userDataService && isOnline && auth.currentUser) {
        // Save to Firebase only if user is authenticated with Firebase
        await userDataService.updateNodePosition(node.id, snappedPosition.x, snappedPosition.y);
        console.log(`Updated node ${node.id} position in Firebase:`, snappedPosition);
      } else {
        // Fallback to localStorage for legacy nodes
        if (node.type === 'opportunity') {
          opportunityService.updatePosition(
            parseInt(node.id),
            node.position.x,
            node.position.y
          );
        } else {
          setNodes(currentNodes => {
            const updatedNodes = currentNodes.map(n =>
              n.id === node.id ? { ...n, position: node.position } : n
            );
            saveNodesAndEdges(updatedNodes, edges);
            return updatedNodes;
          });
        }
      }
    } catch (error) {
      console.error('Error updating node position:', error);
      // Fallback to localStorage on Firebase error
      setNodes(currentNodes => {
        const updatedNodes = currentNodes.map(n =>
          n.id === node.id ? { ...n, position: node.position } : n
        );
        saveNodesAndEdges(updatedNodes, edges);
        return updatedNodes;
      });
    }
  }, [userDataService, isOnline, setNodes, edges, saveNodesAndEdges]);

  // Handle node click
  const onNodeClick = useCallback((event, node) => {
    if (onNodeSelect) {
      // Pass both node data and node ID for Firebase nodes
      onNodeSelect(node.data, node.id);
    }
  }, [onNodeSelect]);

  // Handle edge connection
  const onConnect = useCallback((params) => {
    setEdges((eds) => {
      const newEdges = addEdge(params, eds);
      saveNodesAndEdges(nodes, newEdges);
      return newEdges;
    });
  }, [setEdges, nodes, saveNodesAndEdges]);

  // Handle connection start (for edge-drop node creation)
  const onConnectStart = useCallback((event, { nodeId, handleType }) => {
    setPendingConnection({ nodeId, handleType });
  }, []);

  // Handle connection end (for edge-drop node creation)
  const onConnectEnd = useCallback((event) => {
    if (pendingConnection) {
      const targetIsPane = event.target.classList.contains('react-flow__pane');

      if (targetIsPane) {
        // Get the position where the connection was dropped
        const position = screenToFlowPosition({
          x: event.clientX,
          y: event.clientY,
        });
        const snappedPosition = snapToGridPosition(position);

        setSelectorPosition(snappedPosition);
        setShowNodeSelector(true);
      }

      setPendingConnection(null);
    }
  }, [pendingConnection, screenToFlowPosition, snapToGridPosition]);

  // Create new node
  const createNode = useCallback(async (nodeTypeName, position) => {
    try {
      // Snap position to grid
      const snappedPosition = snapToGridPosition(position);
      const newNode = nodeRegistry.createNode(nodeTypeName, { position: snappedPosition });
      const flowNode = newNode.toFlowNode();

      if (userDataService && isOnline && user?.id && auth.currentUser) {
        // Save to Firebase only if user is authenticated with Firebase (not demo mode)
        const nodeData = {
          type: nodeTypeName,
          position: snappedPosition,
          data: flowNode.data
        };

        console.log('🔍 Debug - Creating node with Firebase user:', auth.currentUser);
        console.log('🔍 Debug - Creating node with user ID:', user.id);
        console.log('🔍 Debug - Node data:', nodeData);

        const firebaseNodeId = await userDataService.createNode(nodeData);
        flowNode.id = firebaseNodeId; // Use Firebase-generated ID

        console.log(`Created node ${firebaseNodeId} in Firebase`);
      }

      setNodes((nds) => {
        const updatedNodes = nds.concat(flowNode);
        if (!userDataService || !isOnline) {
          saveNodesAndEdges(updatedNodes, edges);
        }
        return updatedNodes;
      });

      // If there was a pending connection, create the edge
      if (pendingConnection) {
        const newEdge = {
          id: `${pendingConnection.nodeId}-${flowNode.id}`,
          source: pendingConnection.handleType === 'source' ? pendingConnection.nodeId : flowNode.id,
          target: pendingConnection.handleType === 'source' ? flowNode.id : pendingConnection.nodeId,
        };

        if (userDataService && isOnline && auth.currentUser) {
          // Save edge to Firebase only if user is authenticated with Firebase
          await userDataService.createEdge(newEdge);
          console.log(`Created edge in Firebase:`, newEdge);
        }

        setEdges((eds) => {
          const updatedEdges = eds.concat(newEdge);
          if (!userDataService || !isOnline) {
            saveNodesAndEdges(nodes.concat(flowNode), updatedEdges);
          }
          return updatedEdges;
        });
      }

      setShowNodeSelector(false);
      setPendingConnection(null);
    } catch (error) {
      console.error('Error creating node:', error);
      // Fallback to localStorage
      const snappedPosition = snapToGridPosition(position);
      const newNode = nodeRegistry.createNode(nodeTypeName, { position: snappedPosition });
      const flowNode = newNode.toFlowNode();

      setNodes((nds) => {
        const updatedNodes = nds.concat(flowNode);
        saveNodesAndEdges(updatedNodes, edges);
        return updatedNodes;
      });

      setShowNodeSelector(false);
      setPendingConnection(null);
    }
  }, [userDataService, isOnline, setNodes, setEdges, edges, nodes, pendingConnection, saveNodesAndEdges, snapToGridPosition]);

  // Handle pane click to create nodes
  const onPaneClick = useCallback((event) => {
    // Only show selector if not dragging and no pending connection
    if (!pendingConnection && event.detail === 2) { // Double click
      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });
      const snappedPosition = snapToGridPosition(position);
      setSelectorPosition(snappedPosition);
      setShowNodeSelector(true);
    }
  }, [screenToFlowPosition, pendingConnection, snapToGridPosition]);

  if (isLoading) {
    console.log('🔍 Rendering loading screen - isLoading:', isLoading);
    return (
      <div style={{
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '18px',
        color: '#6b7280'
      }}>
        Loading enhanced flow... (Debug: {isLoading ? 'true' : 'false'})
      </div>
    );
  }

  console.log('🔍 Rendering main flow - nodes:', nodes.length, 'edges:', edges.length);

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onConnectStart={onConnectStart}
        onConnectEnd={onConnectEnd}
        onNodeClick={onNodeClick}
        onNodeDragStop={onNodeDragStop}
        onPaneClick={onPaneClick}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
        connectionLineStyle={{ stroke: '#3b82f6', strokeWidth: 2 }}
        defaultEdgeOptions={{ style: { stroke: '#3b82f6', strokeWidth: 2 } }}
      >
        <Background
          color={snapToGrid ? "#94a3b8" : "#f1f5f9"}
          gap={gridSize}
          size={snapToGrid ? 1 : 0.5}
          variant={snapToGrid ? "dots" : "lines"}
        />
        <Controls />
        <MiniMap
          nodeColor={(node) => {
            const configs = {
              opportunity: '#3b82f6',
              company: '#059669',
              contact: '#7c3aed',
              task: '#f59e0b',
              meeting: '#ef4444',
              note: '#6b7280'
            };
            return configs[node.type] || '#6b7280';
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
        />

        {/* Enhanced Control Panel */}
        <Panel position="top-right">
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {/* Firebase Status Indicator */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              padding: '6px 10px',
              background: isOnline && userDataService && auth.currentUser ? '#dcfce7' : '#fef3c7',
              color: isOnline && userDataService && auth.currentUser ? '#166534' : '#92400e',
              borderRadius: '6px',
              fontSize: '12px',
              fontWeight: '500'
            }}>
              {isOnline && userDataService && auth.currentUser ? <Cloud size={14} /> : <CloudOff size={14} />}
              {isOnline && userDataService && auth.currentUser ? 'Firebase Connected' :
                user?.id === 'demo_user' ? 'Demo Mode' : 'Offline Mode'}
            </div>

            <button
              onClick={() => setShowDashboard(true)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Grid size={16} />
              Dashboard
            </button>

            <button
              onClick={() => {
                const snappedPosition = snapToGridPosition({ x: 200, y: 200 });
                setSelectorPosition(snappedPosition);
                setShowNodeSelector(true);
              }}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#059669',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Plus size={16} />
              Add Node
            </button>

            <button
              onClick={loadNodes}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <RefreshCw size={16} />
              Refresh
            </button>

            <button
              onClick={() => setSnapToGrid(!snapToGrid)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: snapToGrid ? '#3b82f6' : '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Grid size={16} />
              {snapToGrid ? 'Grid On' : 'Grid Off'}
            </button>
          </div>
        </Panel>

        {/* Instructions Panel */}
        <Panel position="bottom-left">
          <div style={{
            background: 'white',
            padding: '12px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            fontSize: '12px',
            maxWidth: '300px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>Quick Actions</div>
            <div style={{ marginBottom: '4px' }}>• Double-click empty space to create node</div>
            <div style={{ marginBottom: '4px' }}>• Drag from node handle and drop to create connected node</div>
            <div style={{ marginBottom: '4px' }}>• Click Dashboard for advanced node management</div>
            <div>• Drag nodes to reposition them</div>
          </div>
        </Panel>
      </ReactFlow>

      {/* Node Type Selector Modal */}
      <NodeTypeSelector
        isOpen={showNodeSelector}
        onClose={() => {
          setShowNodeSelector(false);
          setPendingConnection(null);
        }}
        onSelectType={createNode}
        position={selectorPosition}
      />

      {/* Node Creation Dashboard */}
      <NodeCreationDashboard
        isOpen={showDashboard}
        onClose={() => setShowDashboard(false)}
        onCreateNode={createNode}
      />
    </div>
  );
};

export default EnhancedFlow;
