// Database service layer for CRM operations
import { db } from '../database/schema.js';

// Opportunity operations
export const opportunityService = {
  // Get all opportunities with related data
  getAll: () => {
    const query = `
      SELECT 
        o.*,
        c.name as company_name,
        ct.first_name || ' ' || ct.last_name as contact_name
      FROM opportunities o
      LEFT JOIN companies c ON o.company_id = c.id
      LEFT JOIN contacts ct ON o.contact_id = ct.id
      ORDER BY o.created_at DESC
    `;
    return db.prepare(query).all();
  },

  // Get opportunity by ID
  getById: (id) => {
    const query = `
      SELECT 
        o.*,
        c.name as company_name,
        ct.first_name || ' ' || ct.last_name as contact_name
      FROM opportunities o
      LEFT JOIN companies c ON o.company_id = c.id
      LEFT JOIN contacts ct ON o.contact_id = ct.id
      WHERE o.id = ?
    `;
    return db.prepare(query).get(id);
  },

  // Create new opportunity
  create: (opportunity) => {
    const query = `
      INSERT INTO opportunities (title, description, company_id, contact_id, value, stage, probability, expected_close_date, position_x, position_y)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const result = db.prepare(query).run(
      opportunity.title,
      opportunity.description,
      opportunity.company_id,
      opportunity.contact_id,
      opportunity.value,
      opportunity.stage || 'lead',
      opportunity.probability || 0,
      opportunity.expected_close_date,
      opportunity.position_x || 0,
      opportunity.position_y || 0
    );
    return result.lastInsertRowid;
  },

  // Update opportunity
  update: (id, opportunity) => {
    const query = `
      UPDATE opportunities 
      SET title = ?, description = ?, company_id = ?, contact_id = ?, value = ?, 
          stage = ?, probability = ?, expected_close_date = ?, position_x = ?, position_y = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    return db.prepare(query).run(
      opportunity.title,
      opportunity.description,
      opportunity.company_id,
      opportunity.contact_id,
      opportunity.value,
      opportunity.stage,
      opportunity.probability,
      opportunity.expected_close_date,
      opportunity.position_x,
      opportunity.position_y,
      id
    );
  },

  // Update opportunity position (for drag and drop)
  updatePosition: (id, x, y) => {
    const query = `UPDATE opportunities SET position_x = ?, position_y = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    return db.prepare(query).run(x, y, id);
  },

  // Delete opportunity
  delete: (id) => {
    const query = `DELETE FROM opportunities WHERE id = ?`;
    return db.prepare(query).run(id);
  }
};

// Company operations
export const companyService = {
  getAll: () => {
    return db.prepare('SELECT * FROM companies ORDER BY name').all();
  },

  getById: (id) => {
    return db.prepare('SELECT * FROM companies WHERE id = ?').get(id);
  },

  create: (company) => {
    const query = `
      INSERT INTO companies (name, industry, website, phone, email, address)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    const result = db.prepare(query).run(
      company.name,
      company.industry,
      company.website,
      company.phone,
      company.email,
      company.address
    );
    return result.lastInsertRowid;
  },

  update: (id, company) => {
    const query = `
      UPDATE companies 
      SET name = ?, industry = ?, website = ?, phone = ?, email = ?, address = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    return db.prepare(query).run(
      company.name,
      company.industry,
      company.website,
      company.phone,
      company.email,
      company.address,
      id
    );
  },

  delete: (id) => {
    return db.prepare('DELETE FROM companies WHERE id = ?').run(id);
  }
};

// Contact operations
export const contactService = {
  getAll: () => {
    const query = `
      SELECT c.*, co.name as company_name
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.id
      ORDER BY c.last_name, c.first_name
    `;
    return db.prepare(query).all();
  },

  getById: (id) => {
    const query = `
      SELECT c.*, co.name as company_name
      FROM contacts c
      LEFT JOIN companies co ON c.company_id = co.id
      WHERE c.id = ?
    `;
    return db.prepare(query).get(id);
  },

  getByCompany: (companyId) => {
    return db.prepare('SELECT * FROM contacts WHERE company_id = ? ORDER BY last_name, first_name').all(companyId);
  },

  create: (contact) => {
    const query = `
      INSERT INTO contacts (company_id, first_name, last_name, email, phone, position)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    const result = db.prepare(query).run(
      contact.company_id,
      contact.first_name,
      contact.last_name,
      contact.email,
      contact.phone,
      contact.position
    );
    return result.lastInsertRowid;
  },

  update: (id, contact) => {
    const query = `
      UPDATE contacts 
      SET company_id = ?, first_name = ?, last_name = ?, email = ?, phone = ?, position = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    return db.prepare(query).run(
      contact.company_id,
      contact.first_name,
      contact.last_name,
      contact.email,
      contact.phone,
      contact.position,
      id
    );
  },

  delete: (id) => {
    return db.prepare('DELETE FROM contacts WHERE id = ?').run(id);
  }
};

// Activity operations
export const activityService = {
  getByOpportunity: (opportunityId) => {
    return db.prepare('SELECT * FROM activities WHERE opportunity_id = ? ORDER BY date DESC').all(opportunityId);
  },

  create: (activity) => {
    const query = `
      INSERT INTO activities (opportunity_id, type, description, date)
      VALUES (?, ?, ?, ?)
    `;
    const result = db.prepare(query).run(
      activity.opportunity_id,
      activity.type,
      activity.description,
      activity.date || new Date().toISOString()
    );
    return result.lastInsertRowid;
  }
};
