import React from 'react';
import { Handle, Position } from '@xyflow/react';
import { DollarSign, Calendar, TrendingUp, Building, User } from 'lucide-react';

const OpportunityNode = ({ data, selected }) => {
  const getStageColor = (stage) => {
    const colors = {
      lead: '#6b7280',
      qualified: '#3b82f6',
      proposal: '#f59e0b',
      negotiation: '#10b981',
      closed_won: '#22c55e',
      closed_lost: '#ef4444'
    };
    return colors[stage] || '#6b7280';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No date set';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div 
      className={`opportunity-node ${selected ? 'selected' : ''}`}
      style={{
        background: 'white',
        border: `2px solid ${getStageColor(data.stage)}`,
        borderRadius: '8px',
        padding: '12px',
        minWidth: '280px',
        boxShadow: selected ? '0 4px 12px rgba(0,0,0,0.15)' : '0 2px 8px rgba(0,0,0,0.1)',
        transition: 'all 0.2s ease'
      }}
    >
      {/* Stage indicator */}
      <div 
        className="stage-indicator"
        style={{
          background: getStageColor(data.stage),
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          fontSize: '12px',
          fontWeight: 'bold',
          textTransform: 'uppercase',
          marginBottom: '8px',
          display: 'inline-block'
        }}
      >
        {data.stage.replace('_', ' ')}
      </div>

      {/* Title */}
      <h3 style={{ 
        margin: '0 0 8px 0', 
        fontSize: '16px', 
        fontWeight: 'bold',
        color: '#1f2937'
      }}>
        {data.title}
      </h3>

      {/* Company and Contact */}
      <div style={{ marginBottom: '8px' }}>
        {data.company_name && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
            <Building size={14} style={{ marginRight: '6px', color: '#6b7280' }} />
            <span style={{ fontSize: '14px', color: '#374151' }}>{data.company_name}</span>
          </div>
        )}
        {data.contact_name && (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <User size={14} style={{ marginRight: '6px', color: '#6b7280' }} />
            <span style={{ fontSize: '14px', color: '#374151' }}>{data.contact_name}</span>
          </div>
        )}
      </div>

      {/* Value and Probability */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '8px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DollarSign size={14} style={{ marginRight: '4px', color: '#059669' }} />
          <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#059669' }}>
            {formatCurrency(data.value || 0)}
          </span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <TrendingUp size={14} style={{ marginRight: '4px', color: '#3b82f6' }} />
          <span style={{ fontSize: '14px', color: '#3b82f6' }}>
            {data.probability || 0}%
          </span>
        </div>
      </div>

      {/* Expected Close Date */}
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
        <Calendar size={14} style={{ marginRight: '6px', color: '#6b7280' }} />
        <span style={{ fontSize: '12px', color: '#6b7280' }}>
          Close: {formatDate(data.expected_close_date)}
        </span>
      </div>

      {/* Description */}
      {data.description && (
        <div style={{ 
          fontSize: '12px', 
          color: '#6b7280', 
          marginTop: '8px',
          borderTop: '1px solid #e5e7eb',
          paddingTop: '8px'
        }}>
          {data.description.length > 100 
            ? `${data.description.substring(0, 100)}...` 
            : data.description
          }
        </div>
      )}

      {/* Handles for connections */}
      <Handle
        type="target"
        position={Position.Left}
        style={{ background: getStageColor(data.stage) }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{ background: getStageColor(data.stage) }}
      />
    </div>
  );
};

export default OpportunityNode;
