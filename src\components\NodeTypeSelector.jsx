import React, { useState } from 'react';
import { X, Plus } from 'lucide-react';
import { nodeRegistry } from '../systems/NodeSystem';
import {
  Target, Building, User, CheckSquare, Calendar, FileText
} from 'lucide-react';

const iconMap = {
  Target, Building, User, CheckSquare, Calendar, FileText
};

const NodeTypeSelector = ({ isOpen, onClose, onSelectType, position }) => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  if (!isOpen) return null;

  const nodeTypes = nodeRegistry.getAllNodeTypes();
  const categories = ['all', ...new Set(nodeTypes.map(type => type.category))];

  const filteredTypes = selectedCategory === 'all'
    ? nodeTypes
    : nodeTypes.filter(type => type.category === selectedCategory);

  const handleTypeSelect = (nodeType) => {
    onSelectType(nodeType.name, position);
    onClose();
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '6px',
        padding: '12px',
        width: '75%',
        maxWidth: '420px',
        maxHeight: '60vh',
        overflow: 'auto',
        boxShadow: '0 8px 12px -2px rgba(0, 0, 0, 0.1)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '10px'
        }}>
          <h2 style={{
            margin: 0,
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#1f2937'
          }}>
            Create Node
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '2px',
              borderRadius: '4px'
            }}
          >
            <X size={18} color="#6b7280" />
          </button>
        </div>

        {/* Category Filter */}
        <div style={{ marginBottom: '8px' }}>
          <div style={{
            display: 'flex',
            gap: '4px',
            flexWrap: 'wrap'
          }}>
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                style={{
                  padding: '3px 6px',
                  borderRadius: '12px',
                  border: '1px solid #e5e7eb',
                  background: selectedCategory === category ? '#3b82f6' : 'white',
                  color: selectedCategory === category ? 'white' : '#6b7280',
                  cursor: 'pointer',
                  fontSize: '11px',
                  fontWeight: '500',
                  textTransform: 'capitalize',
                  transition: 'all 0.2s ease'
                }}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Node Types Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(160px, 1fr))',
          gap: '6px'
        }}>
          {filteredTypes.map(nodeType => {
            const IconComponent = iconMap[nodeType.icon] || Target;

            return (
              <div
                key={nodeType.name}
                onClick={() => handleTypeSelect(nodeType)}
                style={{
                  border: '1px solid #e5e7eb',
                  borderRadius: '4px',
                  padding: '6px',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  background: 'white'
                }}
                onMouseEnter={(e) => {
                  e.target.style.borderColor = nodeType.color;
                  e.target.style.boxShadow = '0 1px 4px rgba(0,0,0,0.1)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.borderColor = '#e5e7eb';
                  e.target.style.boxShadow = 'none';
                }}
              >
                {/* Icon and Title */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  marginBottom: '4px'
                }}>
                  <div style={{
                    background: nodeType.color,
                    color: 'white',
                    padding: '4px',
                    borderRadius: '3px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <IconComponent size={14} />
                  </div>
                  <div style={{ flex: 1 }}>
                    <h3 style={{
                      margin: 0,
                      fontSize: '12px',
                      fontWeight: 'bold',
                      color: '#1f2937'
                    }}>
                      {nodeType.label}
                    </h3>
                    <div style={{
                      fontSize: '9px',
                      color: '#6b7280',
                      textTransform: 'capitalize'
                    }}>
                      {nodeType.category}
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p style={{
                  margin: '0 0 4px 0',
                  fontSize: '10px',
                  color: '#6b7280',
                  lineHeight: '1.2',
                  display: '-webkit-box',
                  WebkitLineClamp: 1,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}>
                  {nodeType.description}
                </p>

                {/* Properties Preview and Create Button */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div style={{
                    fontSize: '9px',
                    color: '#9ca3af'
                  }}>
                    {nodeType.properties.length} props
                  </div>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '2px',
                    padding: '2px 4px',
                    background: nodeType.color,
                    color: 'white',
                    borderRadius: '2px',
                    fontSize: '9px',
                    fontWeight: '500'
                  }}>
                    <Plus size={10} />
                    Create
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div style={{
          marginTop: '24px',
          padding: '16px',
          background: '#f9fafb',
          borderRadius: '8px',
          fontSize: '14px',
          color: '#6b7280'
        }}>
          <strong>Tip:</strong> You can create connections between nodes by dragging from the handles on the sides of each node.
        </div>
      </div>
    </div>
  );
};

export default NodeTypeSelector;
