# Firebase Setup Guide

This guide will help you set up Firebase for persistent data storage in the Enhanced CRM Dashboard.

## 🔥 Why Firebase?

Firebase provides:
- **Real-time data synchronization** across devices
- **Automatic backups** and data persistence
- **User-specific data** isolation
- **Offline support** with automatic sync when online
- **Scalable cloud storage** that grows with your needs

## 📋 Setup Steps

### Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click **"Create a project"**
3. Enter project name: `enhanced-crm-dashboard` (or your preferred name)
4. Enable/disable Google Analytics (optional)
5. Click **"Create project"**

### Step 2: Enable Firestore Database

1. In your Firebase project, click **"Firestore Database"** in the left sidebar
2. Click **"Create database"**
3. Choose **"Start in test mode"** (for development)
   - This allows read/write access for 30 days
   - You can configure security rules later
4. Select a location close to your users (e.g., `us-central1`)
5. Click **"Done"**

### Step 3: Get Firebase Configuration

1. In your Firebase project, click the **gear icon** (Project Settings)
2. Scroll down to **"Your apps"** section
3. Click the **web icon** `</>`
4. Register your app:
   - App nickname: `Enhanced CRM Dashboard`
   - Check **"Also set up Firebase Hosting"** (optional)
5. Copy the Firebase configuration object

### Step 4: Update Application Configuration

1. Open `src/firebase/config.js` in your project
2. Replace the placeholder config with your actual Firebase config:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789012",
  appId: "your-actual-app-id"
};
```

### Step 5: Test the Connection

1. Start your development server: `npm run dev`
2. Sign in to the application
3. Create a new node and move it around
4. Check the Firebase status indicator in the top-right panel
5. Refresh the page - your nodes should maintain their positions!

## 🔒 Security Rules (Production)

For production use, update your Firestore security rules:

1. Go to **Firestore Database** > **Rules** in Firebase Console
2. Replace the default rules with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /{collection}/{document} {
      allow read, write: if request.auth != null && 
        resource.data.userId == request.auth.uid;
    }
    
    // Allow users to create documents with their own userId
    match /{collection}/{document} {
      allow create: if request.auth != null && 
        request.resource.data.userId == request.auth.uid;
    }
  }
}
```

## 📊 Data Structure

The application creates these Firestore collections:

- **`nodes`**: All node data (opportunities, companies, contacts, etc.)
- **`edges`**: Connections between nodes
- **`opportunities`**: Legacy opportunity data (for backward compatibility)
- **`companies`**: Company information
- **`contacts`**: Contact details
- **`activities`**: Activity logs

Each document includes:
- `userId`: Links data to specific user
- `created_at`: Timestamp when created
- `updated_at`: Timestamp when last modified

## 🚀 Features Enabled by Firebase

### Real-time Synchronization
- Changes appear instantly across all open tabs/devices
- Multiple users can collaborate on the same data

### Persistent Node Positions
- Node positions are saved immediately when moved
- Positions persist across browser sessions and devices

### User Data Isolation
- Each user sees only their own data
- Secure multi-user support

### Offline Support
- Application works offline using cached data
- Changes sync automatically when connection is restored

## 🔧 Troubleshooting

### "Firebase Connected" not showing?
- Check your Firebase config in `src/firebase/config.js`
- Ensure Firestore is enabled in your Firebase project
- Check browser console for error messages

### Nodes not saving positions?
- Verify you're signed in (not in demo mode)
- Check the Firebase status indicator
- Look for error messages in browser console

### Permission denied errors?
- Update Firestore security rules (see Security Rules section)
- Ensure you're authenticated with Google OAuth

### Offline mode stuck?
- Check your internet connection
- Refresh the page to retry Firebase connection
- Clear browser cache if issues persist

## 💡 Development Tips

### Local Development
- Firebase works great with `localhost:5173`
- No additional setup needed for development

### Testing
- Use Firebase's test mode for development
- Create a separate Firebase project for production

### Monitoring
- Use Firebase Console to view your data
- Monitor usage in the Firebase Console dashboard

## 🎯 Next Steps

Once Firebase is set up:

1. **Test node positioning**: Create nodes and move them around
2. **Test persistence**: Refresh the page and verify positions are saved
3. **Test multi-device**: Open the app on different devices/browsers
4. **Configure security rules** for production use
5. **Monitor usage** in Firebase Console

Your Enhanced CRM Dashboard will now have enterprise-grade data persistence and real-time synchronization! 🚀
