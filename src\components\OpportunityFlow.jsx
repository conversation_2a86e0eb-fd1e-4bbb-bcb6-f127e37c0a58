import React, { useState, useCallback, useEffect } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import OpportunityNode from './OpportunityNode';
import { opportunityService } from '../services/database';
import { Plus, RefreshCw } from 'lucide-react';

const nodeTypes = {
  opportunity: OpportunityNode,
};

const OpportunityFlow = ({ onNodeSelect, onAddOpportunity }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load opportunities from database
  const loadOpportunities = useCallback(async () => {
    setIsLoading(true);
    try {
      const opportunities = opportunityService.getAll();
      
      const flowNodes = opportunities.map((opp, index) => ({
        id: opp.id.toString(),
        type: 'opportunity',
        position: { 
          x: opp.position_x || (index % 3) * 320 + 50, 
          y: opp.position_y || Math.floor(index / 3) * 200 + 50 
        },
        data: {
          ...opp,
          label: opp.title
        }
      }));

      setNodes(flowNodes);
      
      // Create edges based on stage progression (optional)
      const flowEdges = [];
      setEdges(flowEdges);
      
    } catch (error) {
      console.error('Error loading opportunities:', error);
    } finally {
      setIsLoading(false);
    }
  }, [setNodes, setEdges]);

  // Load data on component mount
  useEffect(() => {
    loadOpportunities();
  }, [loadOpportunities]);

  // Handle node drag end - save position to database
  const onNodeDragStop = useCallback((event, node) => {
    try {
      opportunityService.updatePosition(
        parseInt(node.id), 
        node.position.x, 
        node.position.y
      );
    } catch (error) {
      console.error('Error updating node position:', error);
    }
  }, []);

  // Handle node click
  const onNodeClick = useCallback((event, node) => {
    if (onNodeSelect) {
      onNodeSelect(node.data);
    }
  }, [onNodeSelect]);

  // Handle edge connection
  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle add new opportunity
  const handleAddOpportunity = () => {
    if (onAddOpportunity) {
      onAddOpportunity();
    }
  };

  if (isLoading) {
    return (
      <div style={{ 
        height: '100%', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        fontSize: '18px',
        color: '#6b7280'
      }}>
        Loading opportunities...
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onNodeDragStop={onNodeDragStop}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
      >
        <Background color="#f1f5f9" gap={20} />
        <Controls />
        <MiniMap 
          nodeColor={(node) => {
            const colors = {
              lead: '#6b7280',
              qualified: '#3b82f6',
              proposal: '#f59e0b',
              negotiation: '#10b981',
              closed_won: '#22c55e',
              closed_lost: '#ef4444'
            };
            return colors[node.data?.stage] || '#6b7280';
          }}
          maskColor="rgba(0, 0, 0, 0.1)"
        />
        
        {/* Control Panel */}
        <Panel position="top-right">
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={handleAddOpportunity}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <Plus size={16} />
              Add Opportunity
            </button>
            
            <button
              onClick={loadOpportunities}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                padding: '8px 12px',
                background: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              <RefreshCw size={16} />
              Refresh
            </button>
          </div>
        </Panel>

        {/* Stage Legend */}
        <Panel position="bottom-right">
          <div style={{
            background: 'white',
            padding: '12px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            fontSize: '12px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>Stages</div>
            {[
              { stage: 'lead', label: 'Lead', color: '#6b7280' },
              { stage: 'qualified', label: 'Qualified', color: '#3b82f6' },
              { stage: 'proposal', label: 'Proposal', color: '#f59e0b' },
              { stage: 'negotiation', label: 'Negotiation', color: '#10b981' },
              { stage: 'closed_won', label: 'Closed Won', color: '#22c55e' },
              { stage: 'closed_lost', label: 'Closed Lost', color: '#ef4444' }
            ].map(({ stage, label, color }) => (
              <div key={stage} style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
                <div style={{
                  width: '12px',
                  height: '12px',
                  background: color,
                  borderRadius: '2px',
                  marginRight: '8px'
                }} />
                {label}
              </div>
            ))}
          </div>
        </Panel>
      </ReactFlow>
    </div>
  );
};

export default OpportunityFlow;
