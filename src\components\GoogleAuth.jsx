import React, { useState, useEffect, useCallback } from 'react';
import { GoogleLogin } from '@react-oauth/google';
import { signInWithCredential, GoogleAuthProvider, signOut, onAuthStateChanged } from 'firebase/auth';
import { auth } from '../firebase/config';
import { LogIn, LogOut, User } from 'lucide-react';

// Enhanced Google OAuth implementation using @react-oauth/google
const GoogleAuth = ({ onAuthChange }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Memoize the auth change handler to prevent infinite loops
  const handleAuthChangeCallback = useCallback((userData) => {
    onAuthChange?.(userData);
  }, [onAuthChange]);

  useEffect(() => {
    // Listen to Firebase auth state changes
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      console.log('🔍 Firebase auth state changed:', firebaseUser ? 'User signed in' : 'User signed out');

      if (firebaseUser) {
        // User is signed in with Firebase
        console.log('🔍 Firebase user details:', {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: firebaseUser.displayName
        });

        const userData = {
          id: firebaseUser.uid,
          email: firebaseUser.email,
          name: firebaseUser.displayName,
          picture: firebaseUser.photoURL,
          loginTime: new Date().toISOString()
        };

        setUser(userData);
        localStorage.setItem('crm_user', JSON.stringify(userData));
        handleAuthChangeCallback(userData);
        console.log('✅ Firebase auth state: User signed in', firebaseUser.uid);
      } else {
        // User is signed out
        console.log('🔍 Firebase auth state: User signed out');

        // User is signed out - don't restore any saved user data
        // This prevents demo user from being restored when Firebase auth fails
        console.log('🔍 Clearing any saved user data since Firebase user is null');
        localStorage.removeItem('crm_user');
        setUser(null);
        handleAuthChangeCallback(null);
      }
      setIsLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [handleAuthChangeCallback]);

  const handleGoogleSuccess = async (credentialResponse) => {
    console.log('🔍 Google sign-in initiated');
    try {
      // Create Firebase credential from Google token
      const credential = GoogleAuthProvider.credential(credentialResponse.credential);
      console.log('🔍 Created Firebase credential');

      // Sign in to Firebase with the Google credential
      const result = await signInWithCredential(auth, credential);
      const firebaseUser = result.user;
      console.log('🔍 Firebase sign-in successful:', firebaseUser.uid);

      const userData = {
        id: firebaseUser.uid, // Use Firebase UID instead of Google sub
        email: firebaseUser.email,
        name: firebaseUser.displayName,
        picture: firebaseUser.photoURL,
        loginTime: new Date().toISOString()
      };

      setUser(userData);
      localStorage.setItem('crm_user', JSON.stringify(userData));
      handleAuthChangeCallback(userData);

      console.log('✅ Successfully signed in with Firebase:', firebaseUser.uid);
    } catch (error) {
      console.error('❌ Error signing in with Firebase:', error);
      console.error('❌ Error details:', error.code, error.message);

      // Don't fallback to demo mode - show the actual error
      alert(`Firebase Authentication Error: ${error.code}\n\nPlease check:\n1. Google provider is enabled in Firebase Console\n2. Domain is authorized\n3. Firebase configuration is correct`);
    }
  };

  const handleGoogleError = () => {
    console.log('❌ Google Sign-In failed, using demo mode');
    handleDemoLogin();
  };

  const handleDemoLogin = () => {
    // Fallback: simulate login for demo purposes
    const demoUser = {
      id: 'demo_user',
      email: '<EMAIL>',
      name: 'Demo User',
      picture: null,
      loginTime: new Date().toISOString()
    };
    setUser(demoUser);
    localStorage.setItem('crm_user', JSON.stringify(demoUser));
    handleAuthChangeCallback(demoUser);
  };

  const handleLogout = async () => {
    try {
      await signOut(auth);
      setUser(null);
      localStorage.removeItem('crm_user');
      handleAuthChangeCallback(null);
      console.log('Successfully signed out from Firebase');
    } catch (error) {
      console.error('Error signing out:', error);
      // Still clear local state even if Firebase signout fails
      setUser(null);
      localStorage.removeItem('crm_user');
      handleAuthChangeCallback(null);
    }
  };

  if (isLoading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{
          width: '20px',
          height: '20px',
          border: '2px solid #e5e7eb',
          borderTop: '2px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <span style={{ fontSize: '14px', color: '#6b7280' }}>Loading...</span>
      </div>
    );
  }

  if (user) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {user.picture ? (
            <img
              src={user.picture}
              alt={user.name}
              style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                border: '2px solid #e5e7eb'
              }}
            />
          ) : (
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: '#3b82f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '14px',
              fontWeight: 'bold'
            }}>
              {user.name?.charAt(0) || <User size={16} />}
            </div>
          )}
          <div>
            <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
              {user.name}
            </div>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              {user.email}
            </div>
          </div>
        </div>

        <button
          onClick={handleLogout}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '6px 12px',
            background: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: '500'
          }}
        >
          <LogOut size={14} />
          Logout
        </button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', alignItems: 'center' }}>
      <GoogleLogin
        onSuccess={handleGoogleSuccess}
        onError={handleGoogleError}
        text="signin_with"
        shape="rectangular"
        theme="outline"
        size="large"
      />

      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        color: '#6b7280',
        fontSize: '14px'
      }}>
        <div style={{ height: '1px', background: '#e5e7eb', flex: 1 }}></div>
        <span>or</span>
        <div style={{ height: '1px', background: '#e5e7eb', flex: 1 }}></div>
      </div>

      <button
        onClick={handleDemoLogin}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          padding: '8px 16px',
          background: '#f3f4f6',
          color: '#374151',
          border: '1px solid #d1d5db',
          borderRadius: '6px',
          cursor: 'pointer',
          fontSize: '13px',
          fontWeight: '500'
        }}
      >
        <User size={14} />
        Continue with Demo Mode
      </button>

      <div style={{
        fontSize: '11px',
        color: '#9ca3af',
        textAlign: 'center',
        maxWidth: '200px'
      }}>
        Demo mode uses local storage only. Sign in with Google for cloud sync.
      </div>
    </div>
  );
};

export default GoogleAuth;
