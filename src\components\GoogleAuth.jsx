import React, { useState, useEffect, useCallback } from 'react';
import { GoogleLogin } from '@react-oauth/google';
import { LogIn, LogOut, User } from 'lucide-react';

// Enhanced Google OAuth implementation using @react-oauth/google
const GoogleAuth = ({ onAuthChange }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Memoize the auth change handler to prevent infinite loops
  const handleAuthChangeCallback = useCallback((userData) => {
    onAuthChange?.(userData);
  }, [onAuthChange]);

  useEffect(() => {
    // Check if user is already logged in (from localStorage)
    const savedUser = localStorage.getItem('crm_user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        handleAuthChangeCallback(userData);
      } catch (error) {
        console.error('Error parsing saved user data:', error);
        localStorage.removeItem('crm_user');
      }
    }
    setIsLoading(false);
  }, []); // Remove onAuthChange dependency to prevent infinite loop

  const handleGoogleSuccess = (credentialResponse) => {
    try {
      // Decode the JWT token (in production, verify this server-side)
      const payload = JSON.parse(atob(credentialResponse.credential.split('.')[1]));

      const userData = {
        id: payload.sub,
        email: payload.email,
        name: payload.name,
        picture: payload.picture,
        loginTime: new Date().toISOString()
      };

      setUser(userData);
      localStorage.setItem('crm_user', JSON.stringify(userData));
      handleAuthChangeCallback(userData);
    } catch (error) {
      console.error('Error processing Google auth response:', error);
      handleDemoLogin(); // Fallback to demo mode
    }
  };

  const handleGoogleError = () => {
    console.log('Google Sign-In failed, using demo mode');
    handleDemoLogin();
  };

  const handleDemoLogin = () => {
    // Fallback: simulate login for demo purposes
    const demoUser = {
      id: 'demo_user',
      email: '<EMAIL>',
      name: 'Demo User',
      picture: null,
      loginTime: new Date().toISOString()
    };
    setUser(demoUser);
    localStorage.setItem('crm_user', JSON.stringify(demoUser));
    handleAuthChangeCallback(demoUser);
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('crm_user');
    handleAuthChangeCallback(null);
  };

  if (isLoading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div style={{
          width: '20px',
          height: '20px',
          border: '2px solid #e5e7eb',
          borderTop: '2px solid #3b82f6',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <span style={{ fontSize: '14px', color: '#6b7280' }}>Loading...</span>
      </div>
    );
  }

  if (user) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {user.picture ? (
            <img
              src={user.picture}
              alt={user.name}
              style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                border: '2px solid #e5e7eb'
              }}
            />
          ) : (
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: '#3b82f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '14px',
              fontWeight: 'bold'
            }}>
              {user.name?.charAt(0) || <User size={16} />}
            </div>
          )}
          <div>
            <div style={{ fontSize: '14px', fontWeight: '500', color: '#1f2937' }}>
              {user.name}
            </div>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              {user.email}
            </div>
          </div>
        </div>

        <button
          onClick={handleLogout}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            padding: '6px 12px',
            background: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
            fontWeight: '500'
          }}
        >
          <LogOut size={14} />
          Logout
        </button>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', alignItems: 'center' }}>
      <GoogleLogin
        onSuccess={handleGoogleSuccess}
        onError={handleGoogleError}
        text="signin_with"
        shape="rectangular"
        theme="outline"
        size="large"
      />
      <button
        onClick={handleDemoLogin}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          padding: '6px 12px',
          background: '#6b7280',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          fontWeight: '500'
        }}
      >
        <User size={14} />
        Use Demo Mode
      </button>
    </div>
  );
};

export default GoogleAuth;
